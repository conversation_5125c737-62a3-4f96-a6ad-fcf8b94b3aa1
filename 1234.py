import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time

# 初始化pro接口
# 请替换为你的Tushare token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')  
pro = ts.pro_api()

def get_comprehensive_stock_data(ts_codes=None, trade_date=None, start_date=None, end_date=None):
    """
    获取股票综合数据
    
    Parameters:
    ts_codes: list, 股票代码列表，如['000001.SZ', '000002.SZ']
    trade_date: str, 交易日期 YYYYMMDD
    start_date: str, 开始日期 YYYYMMDD  
    end_date: str, 结束日期 YYYYMMDD
    """
    
    if trade_date is None and start_date is None:
        # 默认获取最近一个交易日
        today = datetime.now()
        for i in range(10):  # 往前找10天，确保找到交易日
            check_date = (today - timedelta(days=i)).strftime('%Y%m%d')
            try:
                test_df = pro.daily(trade_date=check_date, limit=1)
                if not test_df.empty:
                    trade_date = check_date
                    break
            except:
                continue
    
    print(f"开始获取数据，日期范围：{start_date or trade_date} 到 {end_date or trade_date}")
    
    # 1. 获取基本股票信息
    print("1. 获取股票基本信息...")
    stock_basic = pro.stock_basic(exchange='', list_status='L', 
                                  fields='ts_code,symbol,name,area,industry,list_date')
    
    if ts_codes:
        stock_basic = stock_basic[stock_basic['ts_code'].isin(ts_codes)]
    
    # 2. 获取日线数据
    print("2. 获取日线行情数据...")
    daily_data_list = []
    
    for ts_code in stock_basic['ts_code']:
        try:
            if trade_date:
                daily = pro.daily(ts_code=ts_code, trade_date=trade_date)
            else:
                daily = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
            
            if not daily.empty:
                daily_data_list.append(daily)
            time.sleep(0.1)  # 避免频率限制
        except Exception as e:
            print(f"获取{ts_code}日线数据失败: {e}")
            continue
    
    if daily_data_list:
        daily_data = pd.concat(daily_data_list, ignore_index=True)
    else:
        daily_data = pd.DataFrame()
    
    # 3. 获取财务数据
    print("3. 获取财务数据...")
    # 获取最新的财务数据
    balancesheet_list = []  # 资产负债表
    income_list = []        # 利润表
    cashflow_list = []      # 现金流量表
    
    for ts_code in stock_basic['ts_code']:
        try:
            # 资产负债表
            balance = pro.balancesheet(ts_code=ts_code, period='20231231', 
                                     fields='ts_code,total_assets,total_hldr_eqy_exc_min_int,total_liab')
            if not balance.empty:
                balancesheet_list.append(balance)
            
            # 利润表 (TTM数据)
            income = pro.income(ts_code=ts_code, period='20231231',
                               fields='ts_code,n_income')
            if not income.empty:
                income_list.append(income)
            
            # 现金流量表
            cashflow = pro.cashflow(ts_code=ts_code, period='20231231',
                                  fields='ts_code,n_cashflow_act')
            if not cashflow.empty:
                cashflow_list.append(cashflow)
            
            time.sleep(0.2)  # 财务数据请求间隔稍长
        except Exception as e:
            print(f"获取{ts_code}财务数据失败: {e}")
            continue
    
    # 合并财务数据
    if balancesheet_list:
        balance_data = pd.concat(balancesheet_list, ignore_index=True)
    else:
        balance_data = pd.DataFrame()
    
    if income_list:
        income_data = pd.concat(income_list, ignore_index=True)
    else:
        income_data = pd.DataFrame()
        
    if cashflow_list:
        cashflow_data = pd.concat(cashflow_list, ignore_index=True)
    else:
        cashflow_data = pd.DataFrame()
    
    # 4. 获取每日指标数据（市值等）
    print("4. 获取每日指标数据...")
    daily_basic_list = []
    
    for ts_code in stock_basic['ts_code']:
        try:
            if trade_date:
                daily_basic = pro.daily_basic(ts_code=ts_code, trade_date=trade_date,
                                            fields='ts_code,trade_date,total_mv,circ_mv')
            else:
                daily_basic = pro.daily_basic(ts_code=ts_code, start_date=start_date, end_date=end_date,
                                            fields='ts_code,trade_date,total_mv,circ_mv')
            
            if not daily_basic.empty:
                daily_basic_list.append(daily_basic)
            time.sleep(0.1)
        except Exception as e:
            print(f"获取{ts_code}每日指标失败: {e}")
            continue
    
    if daily_basic_list:
        daily_basic_data = pd.concat(daily_basic_list, ignore_index=True)
    else:
        daily_basic_data = pd.DataFrame()
    
    # 5. 获取资金流向数据
    print("5. 获取资金流向数据...")
    moneyflow_list = []
    
    for ts_code in stock_basic['ts_code']:
        try:
            if trade_date:
                moneyflow = pro.moneyflow(ts_code=ts_code, trade_date=trade_date)
            else:
                moneyflow = pro.moneyflow(ts_code=ts_code, start_date=start_date, end_date=end_date)
            
            if not moneyflow.empty:
                moneyflow_list.append(moneyflow)
            time.sleep(0.1)
        except Exception as e:
            print(f"获取{ts_code}资金流向失败: {e}")
            continue
    
    if moneyflow_list:
        moneyflow_data = pd.concat(moneyflow_list, ignore_index=True)
    else:
        moneyflow_data = pd.DataFrame()
    
    # 6. 获取指数成分股信息
    print("6. 获取指数成分股信息...")
    index_components = get_index_components_improved(trade_date or end_date)
    
    # 7. 获取申万行业分类信息
    print("7. 获取申万行业分类...")
    sw_industry_data = get_sw_industry_classification_improved()
    print(f"  获取申万行业分类完成: 一级{len(sw_industry_data['sw_l1'])}条")
    
    # 数据整合
    print("8. 整合所有数据...")
    
    # 以日线数据为主表进行合并
    result = daily_data.copy()
    
    if not result.empty:
        # 添加股票名称
        result = result.merge(stock_basic[['ts_code', 'name']], on='ts_code', how='left')
        
        # 添加市值数据
        if not daily_basic_data.empty:
            result = result.merge(daily_basic_data, on=['ts_code', 'trade_date'], how='left')
        
        # 添加财务数据
        if not balance_data.empty:
            result = result.merge(balance_data.rename(columns={
                'total_assets': '总资产',
                'total_hldr_eqy_exc_min_int': '净资产', 
                'total_liab': '总负债'
            }), on='ts_code', how='left')
        
        if not income_data.empty:
            result = result.merge(income_data.rename(columns={
                'n_income': '净利润TTM'
            }), on='ts_code', how='left')
        
        if not cashflow_data.empty:
            result = result.merge(cashflow_data.rename(columns={
                'n_cashflow_act': '现金流TTM'
            }), on='ts_code', how='left')
        
        # 添加资金流向数据
        if not moneyflow_data.empty:
            moneyflow_rename = moneyflow_data.rename(columns={
                'buy_md_amount': '中户资金买入额',
                'sell_md_amount': '中户资金卖出额', 
                'buy_lg_amount': '大户资金买入额',
                'sell_lg_amount': '大户资金卖出额',
                'buy_sm_amount': '散户资金买入额',
                'sell_sm_amount': '散户资金卖出额',
                'buy_elg_amount': '机构资金买入额',
                'sell_elg_amount': '机构资金卖出额'
            })
            result = result.merge(moneyflow_rename, on=['ts_code', 'trade_date'], how='left')
        
        # 添加指数成分股标记
        for index_name, components_dict in index_components.items():
            result[f'{index_name}成分股'] = result['ts_code'].map(components_dict).fillna(0).astype(int)
        
        # 添加申万行业分类
        result['新版申万一级行业名称'] = result['ts_code'].map(sw_industry_data['sw_l1']).fillna('未分类')
        result['新版申万二级行业名称'] = result['ts_code'].map(sw_industry_data['sw_l2']).fillna('未分类') 
        result['新版申万三级行业名称'] = result['ts_code'].map(sw_industry_data['sw_l3']).fillna('未分类')
        
        # 重命名列名为中文
        result = result.rename(columns={
            'ts_code': '股票代码',
            'name': '股票名称', 
            'trade_date': '交易日期',
            'open': '开盘价',
            'high': '最高价', 
            'low': '最低价',
            'close': '收盘价',
            'pre_close': '前收盘价',
            'vol': '成交量',
            'amount': '成交额',
            'circ_mv': '流通市值',
            'total_mv': '总市值'
        })
        
        # 整理列的顺序
        columns_order = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产', '总负债',
            '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额'
        ]
        
        # 添加指数成分股列
        index_columns = ['沪深300成分股', '上证50成分股', '中证500成分股', 
                        '中证1000成分股', '中证2000成分股', '创业板指成分股']
        columns_order.extend(index_columns)
        
        # 添加行业分类列
        columns_order.extend(['新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称'])
        
        # 重新排列列顺序
        existing_columns = [col for col in columns_order if col in result.columns]
        result = result[existing_columns]
    
    print(f"数据获取完成！共获取 {len(result)} 条记录")
    return result

# 使用示例
if __name__ == "__main__":
    print("=== 开始测试 ===")
    
    # 首先测试调试版本
    print("1. 运行调试版本...")
    try:
        index_data, sw_data = debug_classification_data(['000001.SZ', '000002.SZ', '600519.SH'])
        print("调试版本运行成功！")
    except Exception as e:
        print(f"调试版本失败: {e}")
    
    print("\n2. 运行简化版本...")
    try:
        simple_data = get_simple_stock_data(['000001.SZ', '000002.SZ'])
        print("简化版本数据:")
        if not simple_data.empty:
            print(simple_data[['ts_code', 'name', '申万一级行业']].head())
            # 检查指数成分股列
            index_cols = [col for col in simple_data.columns if '成分股' in col]
            if index_cols:
                print("指数成分股情况:")
                print(simple_data[['ts_code', 'name'] + index_cols].head())
        else:
            print("简化版本未获取到数据")
    except Exception as e:
        print(f"简化版本失败: {e}")
    
    print("\n3. 运行完整版本...")
    try:
        # 示例1: 获取指定股票的最新数据
        data1 = get_comprehensive_stock_data(ts_codes=['000001.SZ', '000002.SZ'])
        print("完整版本数据:")
        if not data1.empty:
            print(f"获取到 {len(data1)} 条记录")
            print("前几列数据:")
            print(data1[['股票代码', '股票名称', '收盘价']].head())
            
            # 检查分类数据
            classification_cols = [col for col in data1.columns if '成分股' in col or '行业' in col]
            if classification_cols:
                print("分类信息:")
                print(data1[['股票代码', '股票名称'] + classification_cols].head())
            
            # 保存到Excel
            data1.to_excel('股票综合数据_测试.xlsx', index=False)
            print("数据已保存到 股票综合数据_测试.xlsx")
        else:
            print("完整版本未获取到数据")
            
    except Exception as e:
        print(f"完整版本失败: {e}")
        import traceback
        traceback.print_exc()

# 调试版本 - 单独测试指数成分股和行业分类
def debug_classification_data(ts_codes=['000001.SZ', '000002.SZ', '600519.SH']):
    """
    调试版本：单独测试指数成分股和行业分类获取
    """
    print("=== 调试模式：测试指数成分股和行业分类 ===")
    
    # 测试指数成分股
    print("\n1. 测试指数成分股...")
    index_components = get_index_components_improved()
    
    for index_name, components in index_components.items():
        print(f"{index_name}: {len(components)}只股票")
        # 显示前几只股票
        sample_stocks = list(components.keys())[:5]
        print(f"  样本股票: {sample_stocks}")
    
    # 测试申万行业分类  
    print("\n2. 测试申万行业分类...")
    sw_data = get_sw_industry_classification_improved()
    
    print(f"申万一级行业分类: {len(sw_data['sw_l1'])}条")
    # 显示前几个分类结果
    sample_classifications = list(sw_data['sw_l1'].items())[:10]
    for ts_code, industry in sample_classifications:
        print(f"  {ts_code}: {industry}")
    
    # 测试指定股票的分类情况
    print(f"\n3. 测试指定股票 {ts_codes} 的分类情况...")
    for ts_code in ts_codes:
        print(f"\n股票 {ts_code}:")
        
        # 指数成分股情况
        for index_name, components in index_components.items():
            is_component = components.get(ts_code, 0)
            print(f"  {index_name}成分股: {is_component}")
        
        # 行业分类情况
        industry = sw_data['sw_l1'].get(ts_code, '未分类')
        print(f"  申万一级行业: {industry}")
    
    return index_components, sw_data

# 简化版获取函数 - 用于快速测试
def get_simple_stock_data(ts_codes, trade_date=None):
    """
    简化版本：只获取基本数据和分类信息，便于调试
    """
    print(f"获取简化数据: {ts_codes}")
    
    # 基本信息
    stock_basic = pro.stock_basic(exchange='', list_status='L', 
                                  fields='ts_code,symbol,name')
    stock_basic = stock_basic[stock_basic['ts_code'].isin(ts_codes)]
    
    # 日线数据
    if not trade_date:
        today = datetime.now()
        for i in range(10):
            check_date = (today - timedelta(days=i)).strftime('%Y%m%d')
            try:
                test_df = pro.daily(trade_date=check_date, limit=1)
                if not test_df.empty:
                    trade_date = check_date
                    break
            except:
                continue
    
    daily_data_list = []
    for ts_code in ts_codes:
        daily = pro.daily(ts_code=ts_code, trade_date=trade_date)
        if not daily.empty:
            daily_data_list.append(daily)
        time.sleep(0.1)
    
    if daily_data_list:
        result = pd.concat(daily_data_list, ignore_index=True)
        result = result.merge(stock_basic, on='ts_code', how='left')
    else:
        return pd.DataFrame()
    
    # 获取分类信息
    print("获取分类信息...")
    index_components = get_index_components_improved(trade_date)
    sw_data = get_sw_industry_classification_improved()
    
    # 添加分类信息
    for index_name, components in index_components.items():
        result[f'{index_name}成分股'] = result['ts_code'].map(components).fillna(0).astype(int)
    
    result['申万一级行业'] = result['ts_code'].map(sw_data['sw_l1']).fillna('未分类')
    
    return result

def get_index_components_improved(trade_date=None):
    """
    改进的指数成分股获取函数
    """
    index_components = {}
    
    # 主要指数代码
    indices = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50', 
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    # 如果没有指定日期，尝试多个最近的交易日
    if not trade_date:
        test_dates = []
        today = datetime.now()
        for i in range(30):  # 往前30天寻找交易日
            test_date = (today - timedelta(days=i)).strftime('%Y%m%d')
            test_dates.append(test_date)
    else:
        test_dates = [trade_date]
    
    for index_code, index_name in indices.items():
        success = False
        for test_date in test_dates:
            try:
                components = pro.index_weight(index_code=index_code, trade_date=test_date)
                if not components.empty:
                    components_dict = {code: 1 for code in components['con_code'].tolist()}
                    index_components[index_name] = components_dict
                    print(f"  {index_name}: {len(components)}只成分股 (日期:{test_date})")
                    success = True
                    break
                time.sleep(0.2)
            except Exception as e:
                if "20004" in str(e):  # 没有数据的错误
                    continue
                print(f"  获取{index_name}成分股失败 ({test_date}): {e}")
                continue
        
        if not success:
            print(f"  {index_name}: 未能获取成分股数据")
            index_components[index_name] = {}
        
        time.sleep(0.3)
    
    return index_components

def get_sw_industry_classification_improved():
    """
    改进的申万行业分类获取函数
    """
    sw_data = {'sw_l1': {}, 'sw_l2': {}, 'sw_l3': {}}
    
    try:
        print("  方法1: 通过HS300等主要指数获取行业信息...")
        # 先通过主要股票获取基础行业信息
        basic_stocks = pro.stock_basic(exchange='', list_status='L',
                                     fields='ts_code,name,industry')
        if not basic_stocks.empty:
            for _, row in basic_stocks.iterrows():
                if row['industry'] and row['industry'] != '':
                    sw_data['sw_l1'][row['ts_code']] = row['industry']
        
        print(f"    基础行业信息获取完成: {len(sw_data['sw_l1'])}条")
        
        # 方法2: 通过申万行业指数获取更准确的分类
        print("  方法2: 通过申万行业指数获取...")
        sw_l1_names = {
            '801010.SI': '农林牧渔', '801020.SI': '采掘', '801030.SI': '化工',
            '801040.SI': '钢铁', '801050.SI': '有色金属', '801080.SI': '电子',
            '801110.SI': '家用电器', '801120.SI': '食品饮料', '801130.SI': '纺织服装',
            '801140.SI': '轻工制造', '801150.SI': '医药生物', '801160.SI': '公用事业',
            '801170.SI': '交通运输', '801180.SI': '房地产', '801200.SI': '商业贸易',
            '801210.SI': '休闲服务', '801230.SI': '综合', '801710.SI': '建筑材料',
            '801720.SI': '建筑装饰', '801730.SI': '电气设备', '801740.SI': '国防军工',
            '801750.SI': '计算机', '801760.SI': '传媒', '801770.SI': '通信',
            '801780.SI': '银行', '801790.SI': '非银金融', '801880.SI': '汽车',
            '801890.SI': '机械设备'
        }
        
        # 只获取几个主要行业的示例，避免请求过多
        sample_indices = ['801150.SI', '801780.SI', '801750.SI', '801880.SI', '801120.SI']
        
        for index_code in sample_indices:
            if index_code in sw_l1_names:
                try:
                    industry_name = sw_l1_names[index_code]
                    members = pro.index_member(index_code=index_code)
                    if not members.empty:
                        for ts_code in members['con_code']:
                            sw_data['sw_l1'][ts_code] = industry_name
                        print(f"    {industry_name}: {len(members)}只股票")
                    time.sleep(0.3)
                except Exception as e:
                    print(f"    获取{sw_l1_names.get(index_code, index_code)}失败: {e}")
                    continue
        
        print(f"  申万行业分类最终结果: {len(sw_data['sw_l1'])}条")
        
    except Exception as e:
        print(f"获取申万行业分类失败: {e}")
    
    return sw_data

def get_index_components_improved(trade_date=None):
    """
    改进的指数成分股获取函数
    """
    index_components = {}
    
    # 主要指数代码
    indices = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50', 
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    # 如果没有指定日期，尝试多个最近的交易日
    if not trade_date:
        test_dates = []
        today = datetime.now()
        for i in range(30):  # 往前30天寻找交易日
            test_date = (today - timedelta(days=i)).strftime('%Y%m%d')
            test_dates.append(test_date)
    else:
        test_dates = [trade_date]
    
    for index_code, index_name in indices.items():
        success = False
        for test_date in test_dates:
            try:
                components = pro.index_weight(index_code=index_code, trade_date=test_date)
                if not components.empty:
                    components_dict = {code: 1 for code in components['con_code'].tolist()}
                    index_components[index_name] = components_dict
                    print(f"  {index_name}: {len(components)}只成分股 (日期:{test_date})")
                    success = True
                    break
                time.sleep(0.2)
            except Exception as e:
                if "20004" in str(e):  # 没有数据的错误
                    continue
                print(f"  获取{index_name}成分股失败 ({test_date}): {e}")
                continue
        
        if not success:
            print(f"  {index_name}: 未能获取成分股数据")
            index_components[index_name] = {}
        
        time.sleep(0.3)
    
    return index_components

def batch_process_stocks(batch_size=100):
    """
    分批处理所有股票数据，避免请求过于频繁
    """
    # 获取所有股票代码
    stock_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code')
    all_codes = stock_basic['ts_code'].tolist()
    
    all_data = []
    
    for i in range(0, len(all_codes), batch_size):
        batch_codes = all_codes[i:i+batch_size]
        print(f"处理第 {i//batch_size + 1} 批，股票数量: {len(batch_codes)}")
        
        batch_data = get_comprehensive_stock_data(ts_codes=batch_codes)
        if not batch_data.empty:
            all_data.append(batch_data)
        
        # 批次间休息
        time.sleep(2)
    
    if all_data:
        final_data = pd.concat(all_data, ignore_index=True)
        return final_data
    else:
        return pd.DataFrame()