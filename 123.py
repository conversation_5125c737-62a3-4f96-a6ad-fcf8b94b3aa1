import tushare as ts
import pandas as pd
from datetime import datetime

def init_tushare():
    """
    初始化Tushare，需要先设置token
    请先在tushare官网注册并获取token
    """
    # 需要替换为您的tushare token
    token = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
    ts.set_token(token)
    pro = ts.pro_api()
    return pro

def get_index_stocks(pro, index_code, index_name, start_date='20240101', end_date=None, latest_only=True):
    """
    获取指定指数的成分股
    
    Args:
        pro: tushare pro接口对象
        index_code: 指数代码
        index_name: 指数名称
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD，默认为当前日期
        latest_only: 是否只返回最新数据，False则返回所有历史数据
    
    Returns:
        DataFrame: 成分股数据
    """
    try:
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 获取指数成分股
        df = pro.index_weight(index_code=index_code, 
                            start_date=start_date,
                            end_date=end_date)
        
        if not df.empty:
            df['index_name'] = index_name
            df['index_code'] = index_code
            
            if latest_only:
                # 获取最新的成分股数据
                latest_date = df['trade_date'].max()
                latest_df = df[df['trade_date'] == latest_date].copy()
                print(f"✓ {index_name} ({index_code}) 成功获取 {len(latest_df)} 只成分股 (最新日期: {latest_date})")
                return latest_df
            else:
                # 返回所有历史数据
                dates_count = df['trade_date'].nunique()
                print(f"✓ {index_name} ({index_code}) 成功获取 {len(df)} 条记录，跨越 {dates_count} 个调整日期")
                return df
        else:
            print(f"✗ {index_name} ({index_code}) 未获取到数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"✗ {index_name} ({index_code}) 获取失败: {str(e)}")
        return pd.DataFrame()

def get_all_index_stocks(start_date='20240101', end_date=None, latest_only=True, include_history_analysis=False):
    """
    获取所有指定指数的成分股
    
    Args:
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD，默认为当前日期
        latest_only: 是否只获取最新成分股
        include_history_analysis: 是否包含历史变动分析
    """
    # 初始化tushare
    pro = init_tushare()
    
    if end_date is None:
        end_date = datetime.now().strftime('%Y%m%d')
    
    # 定义各个指数的代码和名称
    indexes = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50', 
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }
    
    all_stocks = []
    results = {}
    
    print("开始获取各指数成分股数据...")
    print(f"日期范围: {start_date} - {end_date}")
    print(f"获取模式: {'仅最新数据' if latest_only else '全部历史数据'}")
    print("="*50)
    
    for index_code, index_name in indexes.items():
        df = get_index_stocks(pro, index_code, index_name, start_date, end_date, latest_only)
        if not df.empty:
            all_stocks.append(df)
            results[index_name] = df
    
    print("="*50)
    
    if all_stocks:
        # 合并所有数据
        combined_df = pd.concat(all_stocks, ignore_index=True)
        
        # 数据统计
        print("\n数据统计:")
        if latest_only:
            for index_name, df in results.items():
                latest_date = df['trade_date'].iloc[0] if not df.empty else 'N/A'
                print(f"{index_name}: {len(df)} 只股票 (日期: {latest_date})")
        else:
            for index_name, df in results.items():
                unique_dates = df['trade_date'].nunique()
                date_range = f"{df['trade_date'].min()} ~ {df['trade_date'].max()}"
                print(f"{index_name}: {len(df)} 条记录，{unique_dates} 个调整日期 ({date_range})")
        
        print(f"\n总计获取 {len(combined_df)} 条记录")
        
        # 历史变动分析
        if include_history_analysis and not latest_only:
            analyze_index_changes(results)
        
        # 保存到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        mode_suffix = '最新' if latest_only else '历史'
        filename = f'指数成分股_{mode_suffix}_{timestamp}.xlsx'
        
        save_to_excel(combined_df, results, filename, latest_only)
        
        print(f"\n数据已保存到: {filename}")
        
        # 显示部分数据样例
        print("\n数据样例 (前10条):")
        display_columns = ['trade_date', 'index_name', 'con_code', 'weight']
        if all(col in combined_df.columns for col in display_columns):
            print(combined_df[display_columns].head(10).to_string(index=False))
        else:
            print(combined_df.head(10))
        
        return combined_df, results
    
    else:
        print("未获取到任何数据，请检查:")
        print("1. Tushare token是否正确设置")
        print("2. 网络连接是否正常")
        print("3. Tushare账户权限是否足够")
        print("4. 日期范围是否合理")
        return None, None

def analyze_index_changes(results):
    """
    分析指数成分股的历史变动情况
    
    Args:
        results: 各指数的历史数据字典
    """
    print("\n" + "="*60)
    print("指数成分股历史变动分析")
    print("="*60)
    
    for index_name, df in results.items():
        if df.empty:
            continue
            
        print(f"\n【{index_name}】变动分析:")
        
        # 按日期分组统计
        date_stats = df.groupby('trade_date').agg({
            'con_code': 'nunique',
            'weight': 'count'
        }).rename(columns={'con_code': '成分股数量', 'weight': '记录数'})
        
        dates = sorted(df['trade_date'].unique())
        print(f"  调整次数: {len(date_stats)} 次")
        print(f"  日期范围: {df['trade_date'].min()} ~ {df['trade_date'].max()}")
        
        # 显示所有调整日期和成分股数量
        print(f"  所有调整日期:")
        for date in dates:
            stock_count = len(df[df['trade_date'] == date])
            print(f"    {date}: {stock_count} 只成分股")
        
        if len(date_stats) > 1:
            print(f"  详细变动情况:")
            
            # 计算所有成分股变动
            for i in range(1, len(dates)):
                prev_date = dates[i-1]
                curr_date = dates[i]
                
                prev_stocks = set(df[df['trade_date'] == prev_date]['con_code'].tolist())
                curr_stocks = set(df[df['trade_date'] == curr_date]['con_code'].tolist())
                
                added = curr_stocks - prev_stocks
                removed = prev_stocks - curr_stocks
                
                if added or removed:
                    print(f"\n    📅 {prev_date} -> {curr_date}:")
                    print(f"       变动统计: 新增 {len(added)} 只, 移除 {len(removed)} 只, 总数 {len(prev_stocks)} -> {len(curr_stocks)}")
                    
                    if added:
                        print(f"       ➕ 新增股票 ({len(added)}只):")
                        added_list = sorted(list(added))
                        # 每行显示5只股票
                        for j in range(0, len(added_list), 5):
                            line_stocks = added_list[j:j+5]
                            print(f"          {', '.join(line_stocks)}")
                    
                    if removed:
                        print(f"       ➖ 移除股票 ({len(removed)}只):")
                        removed_list = sorted(list(removed))
                        # 每行显示5只股票
                        for j in range(0, len(removed_list), 5):
                            line_stocks = removed_list[j:j+5]
                            print(f"          {', '.join(line_stocks)}")
                else:
                    # 即使没有变动也要记录
                    print(f"\n    📅 {prev_date} -> {curr_date}: 无变动 ({len(curr_stocks)}只)")
        else:
            print("  该指数在查询期间无成分股调整")

def save_to_excel(combined_df, results, filename, latest_only):
    """
    保存数据到Excel文件
    
    Args:
        combined_df: 合并的数据
        results: 分指数数据字典
        filename: 文件名
        latest_only: 是否只是最新数据
    """
    with pd.ExcelWriter(filename) as writer:
        # 保存合并数据
        combined_df.to_excel(writer, sheet_name='所有指数成分股', index=False)
        
        # 分别保存各个指数
        for index_name, df in results.items():
            sheet_name = index_name.replace('/', '_')  # Excel工作表名不能包含特殊字符
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        # 如果是历史数据，增加统计汇总表和变动详情表
        if not latest_only:
            # 统计汇总表
            summary_data = []
            change_details = []
            
            for index_name, df in results.items():
                if not df.empty:
                    dates = sorted(df['trade_date'].unique())
                    summary_data.append({
                        '指数名称': index_name,
                        '总记录数': len(df),
                        '调整次数': len(dates),
                        '最早日期': dates[0] if dates else None,
                        '最新日期': dates[-1] if dates else None,
                        '当前成分股数量': len(df[df['trade_date'] == dates[-1]]) if dates else 0,
                        '所有调整日期': ', '.join(dates) if dates else ''
                    })
                    
                    # 变动详情
                    if len(dates) > 1:
                        for i in range(1, len(dates)):
                            prev_date = dates[i-1]
                            curr_date = dates[i]
                            
                            prev_stocks = set(df[df['trade_date'] == prev_date]['con_code'].tolist())
                            curr_stocks = set(df[df['trade_date'] == curr_date]['con_code'].tolist())
                            
                            added = curr_stocks - prev_stocks
                            removed = prev_stocks - curr_stocks
                            
                            change_details.append({
                                '指数名称': index_name,
                                '调整序号': i,
                                '调整前日期': prev_date,
                                '调整后日期': curr_date,
                                '调整前数量': len(prev_stocks),
                                '调整后数量': len(curr_stocks),
                                '新增数量': len(added),
                                '移除数量': len(removed),
                                '新增股票': ', '.join(sorted(added)) if added else '',
                                '移除股票': ', '.join(sorted(removed)) if removed else '',
                                '变动类型': '有变动' if (added or removed) else '无变动'
                            })
            
            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='变动统计汇总', index=False)
            
            if change_details:
                change_df = pd.DataFrame(change_details)
                change_df.to_excel(writer, sheet_name='详细变动记录', index=False)
                
                # 按指数分别保存变动详情
                for index_name in results.keys():
                    index_changes = change_df[change_df['指数名称'] == index_name]
                    if not index_changes.empty:
                        sheet_name = f"{index_name}_变动详情".replace('/', '_')
                        index_changes.to_excel(writer, sheet_name=sheet_name, index=False)

def get_specific_date_stocks(start_date, end_date=None):
    """
    获取特定日期范围的成分股数据
    
    Args:
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD
    """
    print(f"获取 {start_date} 到 {end_date or '当前'} 的成分股数据")
    return get_all_index_stocks(start_date=start_date, 
                              end_date=end_date, 
                              latest_only=False, 
                              include_history_analysis=True)
    """
    获取股票基本信息
    
    Args:
        pro: tushare pro接口对象
        stock_codes: 股票代码列表
    
    Returns:
        DataFrame: 股票基本信息
    """
    try:
        # 获取股票基本信息
        stock_basic = pro.stock_basic(exchange='', 
                                    list_status='L',
                                    fields='ts_code,symbol,name,area,industry,list_date,market')
        
        # 筛选指定股票
        filtered_stocks = stock_basic[stock_basic['ts_code'].isin(stock_codes)]
        
        return filtered_stocks
        
    except Exception as e:
        print(f"获取股票基本信息失败: {str(e)}")
        return pd.DataFrame()

def enhanced_analysis(combined_df, results, pro):
    """
    增强分析功能
    """
    if combined_df is None or combined_df.empty:
        return
    
    print("\n" + "="*60)
    print("增强分析结果")
    print("="*60)
    
    # 获取所有股票代码
    all_stock_codes = combined_df['con_code'].unique().tolist()
    
    # 获取股票基本信息
    stock_info = get_stock_basic_info(pro, all_stock_codes)
    
    if not stock_info.empty:
        # 合并基本信息
        enhanced_df = combined_df.merge(
            stock_info[['ts_code', 'name', 'area', 'industry', 'market']], 
            left_on='con_code', 
            right_on='ts_code', 
            how='left'
        )
        
        # 行业分布分析
        print("\n各指数行业分布 (前5个行业):")
        for index_name, df in results.items():
            index_enhanced = enhanced_df[enhanced_df['index_name'] == index_name]
            if not index_enhanced.empty and 'industry' in index_enhanced.columns:
                industry_dist = index_enhanced['industry'].value_counts().head(5)
                print(f"\n{index_name}:")
                for industry, count in industry_dist.items():
                    print(f"  {industry}: {count}只")
        
        # 重叠股票分析
        print(f"\n重叠股票分析:")
        stock_index_count = combined_df.groupby('con_code')['index_name'].nunique().sort_values(ascending=False)
        overlap_stocks = stock_index_count[stock_index_count > 1]
        
        if not overlap_stocks.empty:
            print(f"同时属于多个指数的股票数量: {len(overlap_stocks)}")
            print("重叠最多的股票:")
            for stock_code, count in overlap_stocks.head(10).items():
                stock_name = stock_info[stock_info['ts_code'] == stock_code]['name'].iloc[0] if not stock_info.empty else stock_code
                index_names = combined_df[combined_df['con_code'] == stock_code]['index_name'].unique()
                print(f"  {stock_name} ({stock_code}): {count}个指数 - {', '.join(index_names)}")
        
        return enhanced_df
    
    return combined_df

if __name__ == "__main__":
    print("Tushare 指数成分股获取工具")
    print("="*60)
    print("注意: 请确保已经设置了有效的Tushare Token")
    print("可以在脚本中修改 token 变量，或使用 ts.set_token() 设置")
    print("="*60)
    
    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 获取最新成分股数据")
    print("2. 获取历史成分股数据并分析变动")
    print("3. 获取特定日期范围的数据")
    
    try:
        choice = input("\n请输入选择 (1/2/3，默认1): ").strip() or "1"
        
        if choice == "1":
            # 获取最新数据
            print("\n获取最新成分股数据...")
            combined_df, results = get_all_index_stocks(
                start_date='20240101',  # 从2024年开始查找最新数据
                latest_only=True
            )
            
        elif choice == "2":
            # 获取历史数据并分析
            start_date = input("请输入开始日期 (YYYYMMDD，默认20240101): ").strip() or "20240101"
            print(f"\n获取 {start_date} 以来的历史数据并分析变动...")
            combined_df, results = get_all_index_stocks(
                start_date=start_date,
                latest_only=False,
                include_history_analysis=True
            )
            
        elif choice == "3":
            # 特定日期范围
            start_date = input("请输入开始日期 (YYYYMMDD): ").strip()
            end_date = input("请输入结束日期 (YYYYMMDD，回车为当前日期): ").strip() or None
            
            if start_date:
                combined_df, results = get_specific_date_stocks(start_date, end_date)
            else:
                print("开始日期不能为空！")
                combined_df, results = None, None
        else:
            print("无效选择，使用默认模式...")
            combined_df, results = get_all_index_stocks(latest_only=True)
        
        # 如果需要增强分析，取消下面的注释
        # if combined_df is not None:
        #     pro = ts.pro_api()
        #     enhanced_df = enhanced_analysis(combined_df, results, pro)
        
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n运行出错: {str(e)}")
    
    print("\n程序执行完成！")